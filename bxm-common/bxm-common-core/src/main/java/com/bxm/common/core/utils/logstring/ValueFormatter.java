package com.bxm.common.core.utils.logstring;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Collection;
import java.util.Date;

/**
 * 值格式化器
 * 用于格式化不同类型的字段值
 * 
 * <AUTHOR>
 * @date 2025-08-29
 */
public class ValueFormatter {
    
    private static final ObjectMapper objectMapper = new ObjectMapper();
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private static final DateTimeFormatter DATETIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    /**
     * 格式化字段值
     * 根据值的类型选择合适的格式化方法
     * 
     * @param value 字段值
     * @param type 字段类型
     * @param config 配置对象
     * @return 格式化后的字符串
     */
    public String formatValue(Object value, Class<?> type, LogStringConfig config) {
        if (value == null) {
            return "null";
        }
        
        // Boolean 类型特殊处理
        if (value instanceof Boolean) {
            return formatBoolean((Boolean) value, config);
        }
        
        // 日期时间类型
        if (value instanceof LocalDate || value instanceof LocalDateTime || value instanceof Date) {
            return formatDate(value);
        }
        
        // 集合类型
        if (value instanceof Collection) {
            return formatCollection((Collection<?>) value, config);
        }
        
        // 数组类型
        if (value.getClass().isArray()) {
            return formatArray(value, config);
        }
        
        // 复杂对象
        if (isComplexObject(value.getClass())) {
            return formatComplexObject(value, config);
        }
        
        // 基本类型直接转字符串
        return String.valueOf(value);
    }
    
    /**
     * 格式化 Boolean 值
     * 根据配置转换为中文或英文
     * 
     * @param value Boolean 值
     * @param config 配置对象
     * @return 格式化后的字符串
     */
    public String formatBoolean(Boolean value, LogStringConfig config) {
        if (value == null) {
            return "null";
        }
        
        if (config != null && config.isChineseBooleanFormat()) {
            return value ? config.getTrueText() : config.getFalseText();
        }
        
        return value.toString();
    }
    
    /**
     * 格式化日期值
     * 
     * @param dateValue 日期对象
     * @return 格式化后的日期字符串
     */
    public String formatDate(Object dateValue) {
        if (dateValue == null) {
            return "null";
        }
        
        if (dateValue instanceof LocalDate) {
            return ((LocalDate) dateValue).format(DATE_FORMATTER);
        }
        
        if (dateValue instanceof LocalDateTime) {
            return ((LocalDateTime) dateValue).format(DATETIME_FORMATTER);
        }
        
        if (dateValue instanceof Date) {
            return new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format((Date) dateValue);
        }
        
        return dateValue.toString();
    }
    
    /**
     * 格式化集合
     * 显示集合大小和前几个元素
     * 
     * @param collection 集合对象
     * @param config 配置对象
     * @return 格式化后的字符串
     */
    public String formatCollection(Collection<?> collection, LogStringConfig config) {
        if (collection == null) {
            return "null";
        }
        
        if (collection.isEmpty()) {
            return "[]";
        }
        
        StringBuilder sb = new StringBuilder();
        sb.append("[size=").append(collection.size());
        
        // 显示前3个元素
        int count = 0;
        int maxElements = 3;
        
        sb.append(", elements=");
        for (Object item : collection) {
            if (count >= maxElements) {
                sb.append("...");
                break;
            }
            
            if (count > 0) {
                sb.append(", ");
            }
            
            // 递归格式化元素
            String formattedItem = formatValue(item, item != null ? item.getClass() : Object.class, config);
            sb.append(formattedItem);
            count++;
        }
        
        sb.append("]");
        return sb.toString();
    }
    
    /**
     * 格式化数组
     * 
     * @param arrayValue 数组对象
     * @param config 配置对象
     * @return 格式化后的字符串
     */
    public String formatArray(Object arrayValue, LogStringConfig config) {
        if (arrayValue == null) {
            return "null";
        }
        
        Class<?> componentType = arrayValue.getClass().getComponentType();
        
        if (componentType == byte.class) {
            byte[] array = (byte[]) arrayValue;
            return "[byte array, length=" + array.length + "]";
        }
        
        if (componentType == int.class) {
            int[] array = (int[]) arrayValue;
            StringBuilder sb = new StringBuilder();
            sb.append("[length=").append(array.length).append(", elements=");
            int maxElements = Math.min(3, array.length);
            for (int i = 0; i < maxElements; i++) {
                if (i > 0) sb.append(", ");
                sb.append(array[i]);
            }
            if (array.length > 3) sb.append("...");
            sb.append("]");
            return sb.toString();
        }
        
        // 对象数组
        Object[] array = (Object[]) arrayValue;
        StringBuilder sb = new StringBuilder();
        sb.append("[length=").append(array.length).append(", elements=");
        int maxElements = Math.min(3, array.length);
        for (int i = 0; i < maxElements; i++) {
            if (i > 0) sb.append(", ");
            String formattedItem = formatValue(array[i], array[i] != null ? array[i].getClass() : Object.class, config);
            sb.append(formattedItem);
        }
        if (array.length > 3) sb.append("...");
        sb.append("]");
        return sb.toString();
    }
    
    /**
     * 格式化复杂对象
     * 使用 JSON 序列化作为回退机制
     * 
     * @param obj 复杂对象
     * @param config 配置对象
     * @return 格式化后的字符串
     */
    public String formatComplexObject(Object obj, LogStringConfig config) {
        if (obj == null) {
            return "null";
        }
        
        try {
            // 尝试 JSON 序列化
            String json = objectMapper.writeValueAsString(obj);
            
            // 如果 JSON 太长，截断它
            if (config != null && json.length() > config.getMaxLength() / 2) {
                return json.substring(0, config.getMaxLength() / 2) + "...}";
            }
            
            return json;
        } catch (JsonProcessingException e) {
            // JSON 序列化失败，使用 toString
            String toString = obj.toString();
            if (config != null && toString.length() > config.getMaxLength() / 2) {
                return toString.substring(0, config.getMaxLength() / 2) + "...";
            }
            return toString;
        }
    }
    
    /**
     * 判断是否为复杂对象
     * 
     * @param type 类型
     * @return 是否为复杂对象
     */
    private boolean isComplexObject(Class<?> type) {
        if (type == null) {
            return false;
        }
        
        // 基本类型和包装类型
        if (type.isPrimitive() || 
            type == String.class ||
            type == Boolean.class ||
            type == Integer.class ||
            type == Long.class ||
            type == Double.class ||
            type == Float.class ||
            type == Short.class ||
            type == Byte.class ||
            type == Character.class) {
            return false;
        }
        
        // 日期时间类型
        if (type.getName().startsWith("java.time.") ||
            type.getName().startsWith("java.util.Date") ||
            type.getName().startsWith("java.sql.")) {
            return false;
        }
        
        // 集合和数组已经单独处理
        if (Collection.class.isAssignableFrom(type) || type.isArray()) {
            return false;
        }
        
        // 其他情况认为是复杂对象
        return true;
    }
}